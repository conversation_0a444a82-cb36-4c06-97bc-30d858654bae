package com.padel.agent.service;

import com.google.api.client.http.FileContent;
import com.google.api.services.youtube.YouTube;
import com.google.api.services.youtube.model.Video;
import com.google.api.services.youtube.model.VideoSnippet;
import com.google.api.services.youtube.model.VideoStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

@Service
public class YoutubeUploaderService {

  public void upload(String title, String privacy) {
    try {
      YouTube youtubeService = YouTubeAuthenticator.getService();

      Video video = new Video();

      VideoSnippet snippet = new VideoSnippet();
      snippet.setTitle(title);
      snippet.setDescription("Uploaded via API");
      snippet.setTags(List.of("padel", "highlight", "api"));
      snippet.setCategoryId("22"); // People & Blogs

      VideoStatus status = new VideoStatus();
      status.setPrivacyStatus(privacy); // "public", "private", "unlisted"

      video.setSnippet(snippet);
      video.setStatus(status);

      File mediaFile = new File("video.mp4");
      FileContent mediaContent = new FileContent("video/*", mediaFile);

      YouTube.Videos.Insert request = youtubeService.videos()
          .insert("snippet,status", video, mediaContent);

      Video response = request.execute();
      System.out.println("✅ Uploaded video: " + response.getId());

    } catch (Exception e) {
      e.printStackTrace();
    }
  }
}