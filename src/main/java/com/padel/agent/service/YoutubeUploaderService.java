package com.padel.agent.service;

import com.google.api.client.http.FileContent;
import com.google.api.services.youtube.YouTube;
import com.google.api.services.youtube.model.Video;
import com.google.api.services.youtube.model.VideoSnippet;
import com.google.api.services.youtube.model.VideoStatus;
import com.padel.agent.model.UploadVideoParameters;
import org.springframework.stereotype.Service;

import java.io.File;

@Service
public class YoutubeUploaderService {

  public void upload(UploadVideoParameters params) {
    try {
      var youtubeService = YouTubeAuthenticator.getService();

      // Создание модели видео
      var video = new Video();

      var snippet = new VideoSnippet();
      snippet.setTitle(params.title());
      snippet.setDescription(params.description());
      snippet.setTags(params.tags());
      snippet.setCategoryId("22");

      var status = new VideoStatus();
      status.setPrivacyStatus(params.privacyStatus());

      video.setSnippet(snippet);
      video.setStatus(status);

      var mediaFile = new File(params.fileName());
      var mediaContent = new FileContent("video/*", mediaFile);

      var request = youtubeService.videos()
          .insert("snippet,status", video, mediaContent);

      var response = request.execute();
      System.out.printf("✅ Uploaded video: %s%n", response.getId());

      var uploadedSnippet = response.getSnippet();
      var thumbnails = uploadedSnippet.getThumbnails();
      if (thumbnails != null) {
        if (thumbnails.getHigh() != null) {
          System.out.println("🖼 High Quality Thumbnail: " + thumbnails.getHigh().getUrl());
        }
        if (thumbnails.getMedium() != null) {
          System.out.println("🖼 Medium Quality Thumbnail: " + thumbnails.getMedium().getUrl());
        }
        if (thumbnails.getDefault() != null) {
          System.out.println("🖼 Default Thumbnail: " + thumbnails.getDefault().getUrl());
        }
      }

    } catch (Exception e) {
      System.err.println("❌ Failed to upload video:");
      e.printStackTrace();
    }
  }
}