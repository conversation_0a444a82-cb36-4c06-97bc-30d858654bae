package com.padel.agent;

import com.google.api.services.youtube.YouTube;
import com.padel.agent.service.YouTubeAuthenticator;
import com.padel.agent.service.YoutubeUploaderService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class AgentApplication {

	public static void main(String[] args) {
		SpringApplication.run(AgentApplication.class, args);
	}

	@Bean
	public CommandLineRunner run(YoutubeUploaderService uploaderService) {
		return args -> {
			System.out.println("🚀 Starting YouTube service initialization...");
			try {
				YouTube youtubeService = YouTubeAuthenticator.getService();
				System.out.println("✅ YouTube service initialized successfully!");
				uploaderService.upload("Java Test", "private");
			} catch (Exception e) {
				System.err.println("❌ Failed to initialize YouTube service: " + e.getMessage());
				e.printStackTrace();
			}
		};
	}

}
