package com.padel.agent.controller;

import com.padel.agent.service.YoutubeUploaderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/upload")
public class UploadController {

  private final YoutubeUploaderService youtubeUploaderService;

  @PostMapping
  public ResponseEntity<String> upload() {
//    youtubeUploaderService.upload("Java Test", "private");
    return ResponseEntity.ok("Browser started");
  }
}