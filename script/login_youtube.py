import undetected_chromedriver as uc
import shutil
import time

from pathlib import Path

EMAIL = "<EMAIL>"
PASSWORD = "04071964DNdn"
SOURCE_PROFILE = Path.home() / "Library/Application Support/Google/Chrome/Profile 1"
TARGET_PROFILE = Path("./chrome-profile/Default")

def copy_chrome_profile():
    if TARGET_PROFILE.exists():
        shutil.rmtree(TARGET_PROFILE)
    shutil.copytree(SOURCE_PROFILE, TARGET_PROFILE)
    print(f"✅ Профиль скопирован в {TARGET_PROFILE}")

def open_studio():
    try:
        options = uc.ChromeOptions()
        options.add_argument("--user-data-dir=" + str(Path("./chrome-profile").resolve()))
        options.add_argument("--profile-directory=Default")
        options.add_argument("--start-maximized")

        driver = uc.Chrome(options=options)
        driver.get("https://studio.youtube.com")

        time.sleep(15)
        driver.quit()
    finally:
        driver.quit()


if __name__ == "__main__":
    copy_chrome_profile()
    open_studio()
