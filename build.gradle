plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.3'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.padel'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.seleniumhq.selenium:selenium-java:4.21.0'
	implementation 'org.seleniumhq.selenium:selenium-support:4.21.0'
	implementation 'io.github.bonigarcia:webdrivermanager:5.7.0'
	implementation 'commons-io:commons-io:2.15.1'
	implementation 'com.google.api-client:google-api-client:1.34.1'
	implementation 'com.google.oauth-client:google-oauth-client-jetty:1.34.1'
	implementation 'com.google.apis:google-api-services-youtube:v3-rev222-1.25.0'
	implementation 'org.springframework.boot:spring-boot-starter-logging'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testCompileOnly 'org.projectlombok:lombok'
	testAnnotationProcessor 'org.projectlombok:lombok'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
}
