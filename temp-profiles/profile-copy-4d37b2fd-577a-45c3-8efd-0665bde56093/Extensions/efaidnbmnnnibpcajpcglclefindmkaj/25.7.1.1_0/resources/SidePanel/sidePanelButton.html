<!-- sidePanelButton.html -->

<style>
    .acrobat-button-container {
        position: fixed;
        right: 0;
        bottom: 110px;
        z-index: 2147483646;
        visibility: visible !important;
        pointer-events: all;
        opacity: 1;
        display: flex;
        align-items: center;
    }

    @media print {
        .acrobat-button-container {
            display: none !important;
        }
    }

    .acrobat-button {
        position: relative;
        background: white;
        display: flex;
        align-items: center;
        pointer-events: all;
        touch-action: none;
        box-shadow: 0px 2px 8px 0px #00000029;
        height: 34px;
        width: 34px;
        border: 1px solid transparent;
        background-clip: padding-box, border-box;
        background-origin: border-box;
        background-image: linear-gradient(white, white),
                        linear-gradient(95.85deg, #D73220 0%, #D92361 33%, #7155FA 100%);
        border-radius: 18px;
        justify-content: right;
        margin-right: 16px;
    }

    .acrobat-button-drag-handle-margin {
        margin-right: 22px;
    }

    .acrobat-button > svg {
        flex-shrink: 0;
        margin: 4px 5px 5px 5px;
        width: 22px;
        height: 22px;
        pointer-events: none;
        fill: #b40000;
    }
    .acrobat-button:has(.close-btn.open),
    .acrobat-button:hover,
    .expand-acrobat-button {
        width: 64px;
    }

    .disabled {
        background: #B1B1B1;
    }

    .tooltip-text {
        width: 160px;
        visibility: hidden;
        opacity: 0;
        background-color: #FFFFFF;
        color: #292929;
        border-radius: 7px;
        padding: 4px 1px 5px 9px;
        position: absolute;
        z-index: 1;
        right: 100%;
        top: 50%;
        transform: translateY(-50%);
        margin-right: 10px;
        transition: opacity 0.3s, visibility 0s linear 2s;
        font-size: 12px;
        line-height: 15px;
        font-family: 'adobe-clean', Adobe Clean, sans-serif;
        filter: drop-shadow(0px 1px 6px rgba(0, 0, 0, 0.12));
    }

    .tooltip-text::after {
       content: '';
       position: absolute;
       top: 50%;
       left: 100%;
       transform: translateY(-50%);
       border-width: 6px;
       border-style: solid;
       border-color: transparent transparent transparent #fff;
    }

    .acrobat-button:hover ~ .tooltip-text,
    .show-tooltip {
        visibility: visible;
        opacity: 1;
        transition-delay: 1s;
        line-height: 1.5;
        font-family: 'adobe-clean', Adobe Clean, sans-serif;
    }

    .close-btn {
        position: relative;
        display: none;
        align-items: center;
        justify-content: center;
    }

    .close-btn svg {
        fill: #464646;
    }

    .close-btn.showCloseButton,
    .acrobat-button:hover .close-btn {
        opacity: 1;
        display: flex;
        justify-content: center;
        width: 22px;
        height: 22px;
        padding: 2px;
        margin: 4px;
    }

    .fab-view-settings-dialog {
        display: none;
        position: absolute;
        bottom: 34px;
        right: 0px;
        z-index: 1;
    }

    .close-btn:hover {
        background: #E6E6E6;
        border-radius: 50%;
    }

    .fab-view-settings-dialog.showDialog {
        display: block;
    }
    .close-btn .burger-icon {
        display: inline;
        margin-bottom: 2px;
    }
    .close-btn .close-icon {
        display: none;
    }
    .close-btn.open .burger-icon {
        display: none;
    }
    .close-btn.open .close-icon {
        display: inline;
    }
    .tooltip-text .tooltip-text-content {
        font-weight: bold;
    }
    .tooltip-text .tooltip-sub-text-text {
        font-style: italic;
        font-size: 11px;
    }
    .draggable-handle {
        display: none;
        position: absolute;
        right: 8px;
        cursor: grab;
    }
    .draggable-handle-visible {
        display: flex;
    }
    .acrobat-button:hover {
        cursor: pointer;
    }
</style>

<div class="acrobat-button-container">
    <div class="acrobat-button" role="button">
        <div class="close-btn" role="button" aria-label="Show Acrobat AI FAB View Settings">
            <svg class="close-icon" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_8552_7892" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="14">
                    <path d="M7.82482 7.00034L10.6374 4.18778C10.7893 4.03592 10.7893 3.78969 10.6374 3.63783L10.3625 3.36292C10.2106 3.21106 9.96446 3.21106 9.81255 3.36292L6.99995 6.17548L4.18734 3.36292C4.03543 3.21106 3.78933 3.21106 3.63743 3.36292L3.36247 3.63783C3.21056 3.78969 3.21056 4.03592 3.36247 4.18778L6.17507 7.00034L3.36247 9.81295C3.21056 9.96481 3.21056 10.2111 3.36247 10.3629L3.63743 10.6378C3.78933 10.7897 4.03543 10.7897 4.18734 10.6378L6.99995 7.82526L9.81255 10.6378C9.96446 10.7897 10.2106 10.7897 10.3625 10.6378L10.6374 10.3629C10.7893 10.2111 10.7893 9.96481 10.6374 9.81295L7.82482 7.00034Z" fill="#222222"/>
                </mask>
                <g mask="url(#mask0_8552_7892)">
                    <rect width="14" height="14" />
                </g>
            </svg>
            <svg class="burger-icon" width="21" height="17" viewBox="0 0 21 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_12188_28876" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="3" width="15" height="14">
                <path d="M15.1285 12.8008H6.37852C6.08867 12.8008 5.85352 13.0359 5.85352 13.3258C5.85352 13.6156 6.08867 13.8508 6.37852 13.8508H15.1285C15.4184 13.8508 15.6535 13.6156 15.6535 13.3258C15.6535 13.0359 15.4184 12.8008 15.1285 12.8008Z" fill="#292929"/>
                <path d="M6.37852 6.85078H15.1285C15.4184 6.85078 15.6535 6.61562 15.6535 6.32578C15.6535 6.03594 15.4184 5.80078 15.1285 5.80078H6.37852C6.08867 5.80078 5.85352 6.03594 5.85352 6.32578C5.85352 6.61562 6.08867 6.85078 6.37852 6.85078Z" fill="#292929"/>
                <path d="M15.1285 9.30078H6.37852C6.08867 9.30078 5.85352 9.53594 5.85352 9.82578C5.85352 10.1156 6.08867 10.3508 6.37852 10.3508H15.1285C15.4184 10.3508 15.6535 10.1156 15.6535 9.82578C15.6535 9.53594 15.4184 9.30078 15.1285 9.30078Z" fill="#292929"/>
                </mask>
                <g mask="url(#mask0_12188_28876)">
                <rect x="3.75391" y="3" width="14" height="14" fill="#464646"/>
                </g>
            </svg>
            <div class="fab-view-settings-dialog"></div>
        </div>
        <svg id="ImportedIcons" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22">
            <path d="m6.21661,16.39534c-1.35034,2.23499-2.67933,3.64758-3.45616,3.64758-.12795.00539-.25276-.04056-.34667-.12762-.14818-.12483-.21629-.32065-.17758-.5105.14764-.80079,1.68628-1.95576,3.98041-3.00946Zm6.49075-3.19656c-.57993.12528-1.1792.27056-1.78689.43544-.4366.11893-.87951.24862-1.32255.38729.23541-.4949.4515-.98554.6398-1.452.24831-.6159.48156-1.232.69564-1.83456.18831.30354.38304.59839.58195.88207.39375.56092.81787,1.09736,1.19205,1.58176Zm-2.99822-10.63684c.08814-.18001.26644-.29855.46653-.31016.51149,0,.62276.63358.62276,1.1638-.06787,1.25011-.29293,2.48669-.66988,3.68055-.85816-2.33642-.73831-3.97319-.41942-4.53419h.00001Zm9.60883,12.20974c-.13359.4402-.56725.71808-1.023.6555-.15594-.00061-.3112-.02054-.46224-.05933-1.07834-.28263-2.07953-.80363-2.92976-1.5246.71719-.11267,1.44198-.17001,2.16797-.17149.48402-.00813.96771.03017,1.44442.11438.43447.09244.92449.33873.80261.98554Zm1.45515-1.0537c-1.01837-.83309-2.33117-1.21735-3.63802-1.06487-.98482.00983-1.96732.09739-2.93836.26187-.62354-.62512-1.19003-1.30467-1.69272-2.03054-.38314-.54455-.73672-1.10931-1.05923-1.69182.54287-1.57384.85986-3.2167.94151-4.87953,0-1.49369-.57564-3.09087-2.19132-3.09087-.56296.01952-1.07528.33036-1.35261.82066-.68906,1.2165-.41298,3.72002.68705,6.26756-.38306,1.17041-.80046,2.31007-1.31196,3.57265-.43687,1.08123-.93191,2.13803-1.48293,3.16579-1.64775.68213-5.15115,2.33642-5.48494,4.16456-.10306.53569.0783,1.08658.47942,1.45629.39145.35044.90217.53779,1.42737.5236,2.10998,0,4.21366-3.003,5.63044-5.58789.80461-.2816,1.62641-.53671,2.44391-.759.8967-.24407,1.76345-.44427,2.58082-.594,1.39014,1.39224,3.25004,2.21523,5.21531,2.30773,1.36751,0,1.87887-.60942,2.06516-1.122.20733-.5893.08589-1.24438-.3189-1.72019Z" fill="var(--iconRed, #d31510)" fill-rule="evenodd"/>
        </svg>
    </div>
    <div class="draggable-handle">
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.60039 13.0699C5.26313 13.0699 5.80039 12.5327 5.80039 11.8699C5.80039 11.2072 5.26313 10.6699 4.60039 10.6699C3.93765 10.6699 3.40039 11.2072 3.40039 11.8699C3.40039 12.5327 3.93765 13.0699 4.60039 13.0699Z" fill="#292929"/>
            <path d="M4.60039 8.27109C5.26313 8.27109 5.80039 7.73384 5.80039 7.07109C5.80039 6.40835 5.26313 5.87109 4.60039 5.87109C3.93765 5.87109 3.40039 6.40835 3.40039 7.07109C3.40039 7.73384 3.93765 8.27109 4.60039 8.27109Z" fill="#292929"/>
            <path d="M4.60039 3.47031C5.26313 3.47031 5.80039 2.93305 5.80039 2.27031C5.80039 1.60757 5.26313 1.07031 4.60039 1.07031C3.93765 1.07031 3.40039 1.60757 3.40039 2.27031C3.40039 2.93305 3.93765 3.47031 4.60039 3.47031Z" fill="#292929"/>
            <path d="M9.4002 13.0699C10.0629 13.0699 10.6002 12.5327 10.6002 11.8699C10.6002 11.2072 10.0629 10.6699 9.4002 10.6699C8.73745 10.6699 8.2002 11.2072 8.2002 11.8699C8.2002 12.5327 8.73745 13.0699 9.4002 13.0699Z" fill="#292929"/>
            <path d="M9.4002 8.27109C10.0629 8.27109 10.6002 7.73384 10.6002 7.07109C10.6002 6.40835 10.0629 5.87109 9.4002 5.87109C8.73745 5.87109 8.2002 6.40835 8.2002 7.07109C8.2002 7.73384 8.73745 8.27109 9.4002 8.27109Z" fill="#292929"/>
            <path d="M9.4002 3.47031C10.0629 3.47031 10.6002 2.93305 10.6002 2.27031C10.6002 1.60757 10.0629 1.07031 9.4002 1.07031C8.73745 1.07031 8.2002 1.60757 8.2002 2.27031C8.2002 2.93305 8.73745 3.47031 9.4002 3.47031Z" fill="#292929"/>
        </svg>  
    </div>
    <div class="tooltip-text">
        <span id="tooltipTextEnabled" class="translate tooltip-text-content"></span>
        <span id="tooltipTextSubText" class="translate tooltip-sub-text-text"></span>
    </div>
</div>
