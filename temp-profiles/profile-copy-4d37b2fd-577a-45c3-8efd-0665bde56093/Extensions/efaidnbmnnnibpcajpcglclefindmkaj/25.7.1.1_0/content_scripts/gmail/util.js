/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import state from"./state.js";import*as messageViewTouchPointService from"./message-view-touch-point-service.js";import*as listViewTouchPointService from"./list-view-touch-point-service.js";import*as nativeViewerTouchPointService from"./native-viewer-touch-point-service.js";import{sendAnalytics,extractFileIdFromDriveUrl}from"../gsuite/util.js";const ACROBAT_ICON_CLASS="acrobat-icon",ACROBAT_LISTENER_ADDED="acrobat-listener-added",ACROBAT_PROCESSED_ATTRIBUTE="acrobat-icon-added",ACROBAT_CONTENT_SCRIPT_DISCONNECT_START="acrobatContentScriptDisconnectStart",LIST_VIEW="LIST_VIEW",DRIVE_USERCONTENT_URL="drive.usercontent.google.com",DRIVE_PDF_URL_PATTERN=`https://${DRIVE_USERCONTENT_URL}/download?id=_id&authuser=_authuser`,createAcrobatIconElement=()=>{const e=document.createElement("img");return e.setAttribute("src",state?.iconURL),e.setAttribute("class","acrobat-icon"),e},getElementBasedOnSelector=(e,t,r)=>{if(e){const i=state?.gmailConfig?.selectors,o=i&&i[r]&&i[r][t];for(let t=0;t<o?.length;t++){let r=e.querySelector(o[t]);if(r)return r}}return null},getClosestElementBasedOnSelector=(e,t,r)=>{if(e){const i=state?.gmailConfig?.selectors,o=i&&i[r]&&i[r][t];for(let t=0;t<o?.length;t++){let r=e.closest(o[t]);if(r)return r}}return null},getArrayElementBasedOnSelector=(e,t,r)=>{if(e){const i=state?.gmailConfig?.selectors,o=i&&i[r]&&i[r][t];for(let t=0;t<o?.length;t++)if(e?.querySelector(o[t])){const r=e?.querySelectorAll(o[t]);if(r&&r.length>0)return r}}return null},getFileDetailsElementInNativeViewer=()=>getElementBasedOnSelector(document,"lightBoxViewerFileDetails","nativeViewer"),getParentElementForNativeViewerPrompt=()=>getElementBasedOnSelector(document,"nativeViewerPromptParentElement","nativeViewer"),isOrphanContentScript=()=>!chrome?.runtime?.id,createURLForAttachment=(e,t,r)=>{let i=e?.replace("disp=safe","disp=inline");return t&&(i=i+"&acrobatPromotionSource="+t),state?.viewerURLPrefix&&(i=`${state.viewerURLPrefix}?pdfurl=${encodeURIComponent(i)}`,r&&(i=i+"&pdffilename="+encodeURIComponent(r))),i},getUserId=()=>{const e=window.location?.pathname?.split("/");return e?.length>3?e[3]:"0"},updateDrivePDFUrl=e=>{const t=extractFileIdFromDriveUrl(e);return t?formDrivePDFUrl(t):(sendAnalytics([["DCBrowserExt:Gmail:ISDAParsingFailed:EmptyDriveFileId"]]),"")},formDrivePDFUrl=e=>DRIVE_PDF_URL_PATTERN.replace("_id",e).replace("_authuser",getUserId()),isDriveFileDirectDownloadLink=e=>e?.includes(DRIVE_USERCONTENT_URL),isDriveLinkAttachmentTouchPointEnabled=()=>state?.gmailConfig?.enableDriveLinkAttachmentPromptInGmail,removeAllAcrobatTouchPoints=()=>{if(document.querySelector("img[acrobat-icon-added]")){const e=document.querySelectorAll('img[acrobat-icon-added="Y"]');for(let t=0;t<e.length;t++)e[t]?.setAttribute("acrobat-icon-added","N")}if(document.querySelector(`[${ACROBAT_LISTENER_ADDED}]`)){const e=document.querySelectorAll(`[${ACROBAT_LISTENER_ADDED}="Y"]`);for(let t=0;t<e.length;t++)e[t]?.setAttribute(ACROBAT_LISTENER_ADDED,"N")}messageViewTouchPointService?.removeAllTouchPoints(),listViewTouchPointService?.removeAllTouchPoints(),nativeViewerTouchPointService?.removeAllTouchPoints()};export{ACROBAT_CONTENT_SCRIPT_DISCONNECT_START,ACROBAT_LISTENER_ADDED,LIST_VIEW,getFileDetailsElementInNativeViewer,getArrayElementBasedOnSelector,getElementBasedOnSelector,getClosestElementBasedOnSelector,createAcrobatIconElement,isOrphanContentScript,createURLForAttachment,getUserId,isDriveFileDirectDownloadLink,isDriveLinkAttachmentTouchPointEnabled,updateDrivePDFUrl,removeAllAcrobatTouchPoints,getParentElementForNativeViewerPrompt};